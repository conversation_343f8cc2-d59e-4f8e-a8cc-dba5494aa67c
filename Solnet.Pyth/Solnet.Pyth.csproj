<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <Description>
            Solnet.Pyth implements functionality to integrate Pyth Network into .Net applications.
        </Description>
        <TargetFramework>net6.0</TargetFramework>
        <IsPackable>true</IsPackable>
    </PropertyGroup>

	<ItemGroup>
		<AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleToAttribute">
			<_Parameter1>Solnet.Pyth.Test</_Parameter1>
		</AssemblyAttribute>
	</ItemGroup>

    <ItemGroup>
      <PackageReference Include="Solnet.Rpc" Version="6.0.13" />
      <PackageReference Include="Solnet.Wallet" Version="6.0.13" />
      <PackageReference Include="Solnet.Programs" Version="6.0.13" />
    </ItemGroup>

    <Import Project="..\SharedBuildProperties.props" />
</Project>
