<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <Description>
          Solnet.Raydium implements functionality to integrate Raydium project into .Net applications.
        </Description>
        <TargetFramework>net5.0</TargetFramework>

        <IsPackable>false</IsPackable>

        <RootNamespace>Solnet.Raydium</RootNamespace>
    </PropertyGroup>

    <ItemGroup>
        <AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleToAttribute">
            <_Parameter1>Solnet.Raydium.Test</_Parameter1>
        </AssemblyAttribute>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.Extensions.Logging" Version="*******" />
        <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="*******" />
        <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="*******" />
        <PackageReference Include="Solnet.Programs" Version="0.4.3" />
        <PackageReference Include="Solnet.Rpc" Version="0.4.3" />
        <PackageReference Include="Solnet.Wallet" Version="0.4.3" />
    </ItemGroup>

    <Import Project="..\SharedBuildProperties.props" />
</Project>
