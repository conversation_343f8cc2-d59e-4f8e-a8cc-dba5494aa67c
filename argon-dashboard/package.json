{"name": "argon-dashboard", "version": "1.1.0", "description": "Argon Dashboard. Coded by Creative Tim", "main": "index.html", "directories": {"example": "root"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/creativetimofficial/argon-dashboard.git"}, "keywords": ["argon dashboard", "bootstrap admin", "bootstrap dashboard", "argon design admin", "argon design", "creative tim", "html dashboard", "html css dashboard", "web dashboard", "freebie", "free bootstrap dashboard", "css3 dashboard", "bootstrap admin", "bootstrap dashboard", "frontend", "responsive bootstrap dashboard"], "author": "Creative Tim <<EMAIL>> (https://www.creative-tim.com/)", "license": "MIT", "bugs": {"url": "https://github.com/creativetimofficial/argon-dashboard/issues"}, "dependencies": {"@elsa-workflows/elsa-workflow-designer": "0.0.61"}, "devDependencies": {"gulp": "^4.0.2", "gulp-autoprefixer": "^7.0.1", "gulp-clean": "^0.4.0", "gulp-install": "^1.1.0", "gulp-sass": "^4.0.2", "gulp-sourcemaps": "^2.6.5", "gulp-open": "^3.0.1"}, "homepage": "http://demos.creative-tim.com/argon-dashboard/index.html"}