<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    
    <AssemblyName>Solnet.Rpc</AssemblyName>
    <RootNamespace>Solnet.Rpc</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleToAttribute">
      <_Parameter1>Solnet.Rpc.Test</_Parameter1>
    </AssemblyAttribute>
    <AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleToAttribute">
      <_Parameter1>Solnet.Extensions.Test</_Parameter1>
    </AssemblyAttribute>
    <AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleToAttribute">
      <_Parameter1>DynamicProxyGenAssembly2</_Parameter1>
    </AssemblyAttribute>
    <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="9.0.0" />
    <ProjectReference Include="..\Solnet.Wallet\Solnet.Wallet.csproj" />
  </ItemGroup>

  <Import Project="..\..\SharedBuildProperties.props" />
</Project>
