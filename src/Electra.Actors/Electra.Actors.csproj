<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <Title>Core Package for the Electra Web Platform</Title>
    <Authors>Microbian Systems</Authors>
    <Description>Core components for the Electra web application platform</Description>
    <Copyright>Microbian Systems 2021</Copyright>
    <PackageProjectUrl>http://Electra.pro</PackageProjectUrl>
    <RepositoryUrl>http://github.com/Electra</RepositoryUrl>
    <PackageTags>Electra, web, asp.net core, .net, Electra</PackageTags>
    <IsPackable>false</IsPackable>
    <RootNamespace>Electra.Actors</RootNamespace>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.Orleans.Analyzers" Version="9.1.2">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Orleans.Client" Version="9.1.2" />
    <PackageReference Include="Microsoft.Orleans.Core" Version="9.1.2" />
    <PackageReference Include="Microsoft.Orleans.Core.Abstractions" Version="9.1.2" />
    <PackageReference Include="Microsoft.Orleans.Sdk" Version="9.1.2" />
    <PackageReference Include="NewId" Version="4.0.1" />
    <PackageReference Include="Polly" Version="8.5.2" />
    <PackageReference Include="Proto.Actor" Version="1.7.0" />
    <PackageReference Include="Proto.Actor.Extensions" Version="0.3.0" />
    <PackageReference Include="Proto.Serialization.Json" Version="0.2.0.1" />
    <PackageReference Include="ThrowGuard" Version="1.0.7" />
    <PackageReference Include="Z.ExtensionMethods" Version="2.1.1" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Electra.Core\Electra.Core.csproj" />
    <ProjectReference Include="..\Electra.Persistence\Electra.Persistence.csproj" />
  </ItemGroup>
</Project>