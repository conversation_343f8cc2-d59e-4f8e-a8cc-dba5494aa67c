namespace Electra.Persistence;

public sealed class ApiAuthContext(DbContextOptions<ApiAuthContext> options)
    : DbContext(options)
{
    public DbSet<ApiAccountModel> ApiAccounts { get; set; }
    public DbSet<ApiClaimsModel> Claims { get; set; }
    private const string schemaName = "apiauth";

    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);

        builder.Entity<ApiAccountModel>()
            .ToTable("ApiAccounts", schema: schemaName);
        builder.Entity<ApiAccountModel>()
            .<PERSON><PERSON><PERSON>(i => i.Id);
        builder.Entity<ApiAccountModel>()
            .HasIndex(i => i.ApiKey, "ix_apikey")
            .IsUnique();
        builder.Entity<ApiAccountModel>()
            .HasIndex(i => i.Email);
        builder.Entity<ApiAccountModel>()
            .HasIndex(i => i.Enabled);
        builder.Entity<ApiAccountModel>()
            .HasIndex(i => i.CreateDate);
        builder.Entity<ApiAccountModel>()
            .HasIndex(i => i.ModifiedDate);

        builder.Entity<ApiClaimsModel>()
            .HasIndex(i => i.ClaimKey);
        builder.Entity<ApiClaimsModel>()
            .HasIndex(i => i.ClaimValue);

        builder.Entity<ApiAccountModel>()
            .HasMany<ApiClaimsModel>()
            .WithOne();

        builder.Entity<ApiClaimsModel>()
            .ToTable("ApiClaims", schema: schemaName)
            .HasKey(pk => pk.Id);
        builder.Entity<ApiClaimsModel>()
            .HasOne<ApiAccountModel>()
            .WithMany(m => m.Claims)
            .HasForeignKey(m => m.AccountId);
    }
}