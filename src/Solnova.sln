
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.13.35931.197
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Solnet.Wallet", "D:\proj\microbians\Solnova\src\Solnet\src\Solnet.Wallet\Solnet.Wallet.csproj", "{39D73BB2-14B4-4014-960D-2CCE6AB47E02}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Solnet.Rpc", "D:\proj\microbians\Solnova\src\Solnet\src\Solnet.Rpc\Solnet.Rpc.csproj", "{12526E4F-44CA-41DF-8DAE-D8835C9CD516}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Solnet.Pyth", "D:\proj\microbians\Solnova\src\Solnet.Pyth\Solnet.Pyth\Solnet.Pyth.csproj", "{E42439C9-7026-49CE-888C-DD6B94EF1F5C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Solnet.Programs", "D:\proj\microbians\Solnova\src\Solnet\src\Solnet.Programs\Solnet.Programs.csproj", "{38FC4C91-0C91-47EE-81A5-5F891E839114}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Solnet.Raydium", "D:\proj\microbians\Solnova\src\Solnet.Raydium\Solnet.Raydium\Solnet.Raydium.csproj", "{6A87EE70-70BA-4240-A29D-AF33E65DCA05}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Solnet.Moonshot", "D:\proj\microbians\Solnova\src\Solnet.Moonshot\Solnet.Moonshot\Solnet.Moonshot.csproj", "{63B76F77-C165-423A-B8A9-11BC9864C35C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Solnet.KeyStore", "D:\proj\microbians\Solnova\src\Solnet\src\Solnet.KeyStore\Solnet.KeyStore.csproj", "{8B76B50E-7A3A-4191-B39A-6686045FAF20}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "__Misc__", "__Misc__", "{266C9744-430A-4EF9-8031-3C38400A2B9A}"
	ProjectSection(SolutionItems) = preProject
		clean.ps1 = clean.ps1
		Directory.Build.props = Directory.Build.props
		nuget-publish.sh = nuget-publish.sh
		nuget-update.sh = nuget-update.sh
		NuGet.Config = NuGet.Config
		pack.sh = pack.sh
		rm-bin-obj.sh = rm-bin-obj.sh
		rm-vscode.sh = rm-vscode.sh
		run-migrations.ps1 = run-migrations.ps1
		Solnova.sln = Solnova.sln
		update-database.ps1 = update-database.ps1
		upgrade-packages.ps1 = upgrade-packages.ps1
		upgrade.ps1 = upgrade.ps1
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Solnova", "Solnova\Solnova\Solnova.csproj", "{CD18BEC6-CB25-474A-B89B-A6A586F5176F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Solnova.Shared", "Solnova\Solnova.Shared\Solnova.Shared.csproj", "{093F4291-098F-4885-848A-4BB89416D288}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Solnova.Web", "Solnova\Solnova.Web\Solnova.Web.csproj", "{0FCD946A-26D6-4D95-94DB-169D82B8595D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Electra.Common", "Electra\src\Electra.Common\Electra.Common.csproj", "{AE013A11-6C4F-42A4-A61F-6C6D2C65E5BD}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Electra.Auth", "Electra\src\Electra.Auth\Electra.Auth.csproj", "{3D19F119-78BC-4F0B-91A3-F8FB78331285}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Electra.Core", "Electra\src\Electra.Core\Electra.Core.csproj", "{341CFAB9-2496-46A4-B570-919FDB8E988C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Electra.Caching", "Electra\src\Electra.Caching\Electra.Caching.csproj", "{FD44F415-40A7-41D2-AF23-CF439720D0F2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Electra.Events", "Electra\src\Electra.Events\Electra.Events.csproj", "{6C1E3CB3-6D84-4BE5-AAB9-F24B97AC762D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Electra.Persistence", "Electra\src\Electra.Persistence\Electra.Persistence.csproj", "{13ED7770-4DB4-44D0-A8CE-24E068B645C0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Electra.Persistence.Marten", "Electra\src\Electra.Persistence.Marten\Electra.Persistence.Marten.csproj", "{48B2494D-4F32-4B2E-915A-58690DCA58A3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Electra.Web", "Electra\src\Electra.Web\Electra.Web.csproj", "{0338AD7A-91C9-4DEA-AFBE-78F935B3FBA3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Electra.Models", "Electra\src\Electra.Models\Electra.Models.csproj", "{991AE299-BE5B-4A7F-827E-25D2BFC52EA9}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Electra.Services", "Electra\src\Electra.Services\Electra.Services.csproj", "{FBD5241E-2D37-4B1B-ACE9-5E734CE643EE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Electra.SignalR", "Electra\src\Electra.SignalR\Electra.SignalR.csproj", "{98AB770C-D12C-4B15-9F28-81607964FD1E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Electra.Validators", "Electra\src\Electra.Validators\Electra.Validators.csproj", "{5E4B0D67-F6B1-47CF-8D7D-C7A762A013EE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Electra.Actors", "Electra\src\Electra.Actors\Electra.Actors.csproj", "{984C6CA5-C2D7-40CB-8EEF-26D77BFFD57F}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Electra", "Electra", "{59EA79DD-CBC3-4E57-B3BF-E21E68CCB457}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{39D73BB2-14B4-4014-960D-2CCE6AB47E02}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{39D73BB2-14B4-4014-960D-2CCE6AB47E02}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{39D73BB2-14B4-4014-960D-2CCE6AB47E02}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{39D73BB2-14B4-4014-960D-2CCE6AB47E02}.Release|Any CPU.Build.0 = Release|Any CPU
		{12526E4F-44CA-41DF-8DAE-D8835C9CD516}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{12526E4F-44CA-41DF-8DAE-D8835C9CD516}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{12526E4F-44CA-41DF-8DAE-D8835C9CD516}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{12526E4F-44CA-41DF-8DAE-D8835C9CD516}.Release|Any CPU.Build.0 = Release|Any CPU
		{E42439C9-7026-49CE-888C-DD6B94EF1F5C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E42439C9-7026-49CE-888C-DD6B94EF1F5C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E42439C9-7026-49CE-888C-DD6B94EF1F5C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E42439C9-7026-49CE-888C-DD6B94EF1F5C}.Release|Any CPU.Build.0 = Release|Any CPU
		{38FC4C91-0C91-47EE-81A5-5F891E839114}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{38FC4C91-0C91-47EE-81A5-5F891E839114}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{38FC4C91-0C91-47EE-81A5-5F891E839114}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{38FC4C91-0C91-47EE-81A5-5F891E839114}.Release|Any CPU.Build.0 = Release|Any CPU
		{6A87EE70-70BA-4240-A29D-AF33E65DCA05}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6A87EE70-70BA-4240-A29D-AF33E65DCA05}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6A87EE70-70BA-4240-A29D-AF33E65DCA05}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6A87EE70-70BA-4240-A29D-AF33E65DCA05}.Release|Any CPU.Build.0 = Release|Any CPU
		{63B76F77-C165-423A-B8A9-11BC9864C35C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{63B76F77-C165-423A-B8A9-11BC9864C35C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{63B76F77-C165-423A-B8A9-11BC9864C35C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{63B76F77-C165-423A-B8A9-11BC9864C35C}.Release|Any CPU.Build.0 = Release|Any CPU
		{8B76B50E-7A3A-4191-B39A-6686045FAF20}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8B76B50E-7A3A-4191-B39A-6686045FAF20}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8B76B50E-7A3A-4191-B39A-6686045FAF20}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8B76B50E-7A3A-4191-B39A-6686045FAF20}.Release|Any CPU.Build.0 = Release|Any CPU
		{CD18BEC6-CB25-474A-B89B-A6A586F5176F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CD18BEC6-CB25-474A-B89B-A6A586F5176F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CD18BEC6-CB25-474A-B89B-A6A586F5176F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CD18BEC6-CB25-474A-B89B-A6A586F5176F}.Release|Any CPU.Build.0 = Release|Any CPU
		{093F4291-098F-4885-848A-4BB89416D288}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{093F4291-098F-4885-848A-4BB89416D288}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{093F4291-098F-4885-848A-4BB89416D288}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{093F4291-098F-4885-848A-4BB89416D288}.Release|Any CPU.Build.0 = Release|Any CPU
		{0FCD946A-26D6-4D95-94DB-169D82B8595D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0FCD946A-26D6-4D95-94DB-169D82B8595D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0FCD946A-26D6-4D95-94DB-169D82B8595D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0FCD946A-26D6-4D95-94DB-169D82B8595D}.Release|Any CPU.Build.0 = Release|Any CPU
		{AE013A11-6C4F-42A4-A61F-6C6D2C65E5BD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AE013A11-6C4F-42A4-A61F-6C6D2C65E5BD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AE013A11-6C4F-42A4-A61F-6C6D2C65E5BD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AE013A11-6C4F-42A4-A61F-6C6D2C65E5BD}.Release|Any CPU.Build.0 = Release|Any CPU
		{3D19F119-78BC-4F0B-91A3-F8FB78331285}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3D19F119-78BC-4F0B-91A3-F8FB78331285}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3D19F119-78BC-4F0B-91A3-F8FB78331285}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3D19F119-78BC-4F0B-91A3-F8FB78331285}.Release|Any CPU.Build.0 = Release|Any CPU
		{341CFAB9-2496-46A4-B570-919FDB8E988C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{341CFAB9-2496-46A4-B570-919FDB8E988C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{341CFAB9-2496-46A4-B570-919FDB8E988C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{341CFAB9-2496-46A4-B570-919FDB8E988C}.Release|Any CPU.Build.0 = Release|Any CPU
		{FD44F415-40A7-41D2-AF23-CF439720D0F2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FD44F415-40A7-41D2-AF23-CF439720D0F2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FD44F415-40A7-41D2-AF23-CF439720D0F2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FD44F415-40A7-41D2-AF23-CF439720D0F2}.Release|Any CPU.Build.0 = Release|Any CPU
		{6C1E3CB3-6D84-4BE5-AAB9-F24B97AC762D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6C1E3CB3-6D84-4BE5-AAB9-F24B97AC762D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6C1E3CB3-6D84-4BE5-AAB9-F24B97AC762D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6C1E3CB3-6D84-4BE5-AAB9-F24B97AC762D}.Release|Any CPU.Build.0 = Release|Any CPU
		{13ED7770-4DB4-44D0-A8CE-24E068B645C0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{13ED7770-4DB4-44D0-A8CE-24E068B645C0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{13ED7770-4DB4-44D0-A8CE-24E068B645C0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{13ED7770-4DB4-44D0-A8CE-24E068B645C0}.Release|Any CPU.Build.0 = Release|Any CPU
		{48B2494D-4F32-4B2E-915A-58690DCA58A3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{48B2494D-4F32-4B2E-915A-58690DCA58A3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{48B2494D-4F32-4B2E-915A-58690DCA58A3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{48B2494D-4F32-4B2E-915A-58690DCA58A3}.Release|Any CPU.Build.0 = Release|Any CPU
		{0338AD7A-91C9-4DEA-AFBE-78F935B3FBA3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0338AD7A-91C9-4DEA-AFBE-78F935B3FBA3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0338AD7A-91C9-4DEA-AFBE-78F935B3FBA3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0338AD7A-91C9-4DEA-AFBE-78F935B3FBA3}.Release|Any CPU.Build.0 = Release|Any CPU
		{991AE299-BE5B-4A7F-827E-25D2BFC52EA9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{991AE299-BE5B-4A7F-827E-25D2BFC52EA9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{991AE299-BE5B-4A7F-827E-25D2BFC52EA9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{991AE299-BE5B-4A7F-827E-25D2BFC52EA9}.Release|Any CPU.Build.0 = Release|Any CPU
		{FBD5241E-2D37-4B1B-ACE9-5E734CE643EE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FBD5241E-2D37-4B1B-ACE9-5E734CE643EE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FBD5241E-2D37-4B1B-ACE9-5E734CE643EE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FBD5241E-2D37-4B1B-ACE9-5E734CE643EE}.Release|Any CPU.Build.0 = Release|Any CPU
		{98AB770C-D12C-4B15-9F28-81607964FD1E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{98AB770C-D12C-4B15-9F28-81607964FD1E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{98AB770C-D12C-4B15-9F28-81607964FD1E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{98AB770C-D12C-4B15-9F28-81607964FD1E}.Release|Any CPU.Build.0 = Release|Any CPU
		{5E4B0D67-F6B1-47CF-8D7D-C7A762A013EE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5E4B0D67-F6B1-47CF-8D7D-C7A762A013EE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5E4B0D67-F6B1-47CF-8D7D-C7A762A013EE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5E4B0D67-F6B1-47CF-8D7D-C7A762A013EE}.Release|Any CPU.Build.0 = Release|Any CPU
		{984C6CA5-C2D7-40CB-8EEF-26D77BFFD57F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{984C6CA5-C2D7-40CB-8EEF-26D77BFFD57F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{984C6CA5-C2D7-40CB-8EEF-26D77BFFD57F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{984C6CA5-C2D7-40CB-8EEF-26D77BFFD57F}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {E8FA8B1C-695E-46E4-BCE4-53BEB051E55F}
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
	EndGlobalSection
EndGlobal
