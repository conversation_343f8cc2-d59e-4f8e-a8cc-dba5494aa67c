
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.13.35931.197
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Solnova", "Solnova\Solnova.csproj", "{580AB63B-7A4E-4CD0-8683-AAAF5550D764}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Solnova.UI", "Solnova.UI\Solnova.UI.csproj", "{DAF2BB02-74F0-4C4D-9248-43EC03778F20}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Solnet.Wallet", "D:\proj\microbians\Solnova\src\Solnet\src\Solnet.Wallet\Solnet.Wallet.csproj", "{39D73BB2-14B4-4014-960D-2CCE6AB47E02}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Solnet.Rpc", "D:\proj\microbians\Solnova\src\Solnet\src\Solnet.Rpc\Solnet.Rpc.csproj", "{12526E4F-44CA-41DF-8DAE-D8835C9CD516}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Solnet.Pyth", "D:\proj\microbians\Solnova\src\Solnet.Pyth\Solnet.Pyth\Solnet.Pyth.csproj", "{E42439C9-7026-49CE-888C-DD6B94EF1F5C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Solnet.Extensions", "D:\proj\microbians\Solnova\src\Solnet\src\Solnet.Extensions\Solnet.Extensions.csproj", "{A9014AAE-B3CA-4130-896B-9EDA134FD8C6}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Solnet.Programs", "D:\proj\microbians\Solnova\src\Solnet\src\Solnet.Programs\Solnet.Programs.csproj", "{38FC4C91-0C91-47EE-81A5-5F891E839114}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Solnet.Raydium", "D:\proj\microbians\Solnova\src\Solnet.Raydium\Solnet.Raydium\Solnet.Raydium.csproj", "{6A87EE70-70BA-4240-A29D-AF33E65DCA05}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Solnet.Moonshot", "D:\proj\microbians\Solnova\src\Solnet.Moonshot\Solnet.Moonshot\Solnet.Moonshot.csproj", "{63B76F77-C165-423A-B8A9-11BC9864C35C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Solnet.KeyStore", "D:\proj\microbians\Solnova\src\Solnet\src\Solnet.KeyStore\Solnet.KeyStore.csproj", "{8B76B50E-7A3A-4191-B39A-6686045FAF20}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Solnet.Extensions", "D:\proj\microbians\Solnova\src\Solnet\src\Solnet.Extensions\Solnet.Extensions.csproj", "{EA8E4102-CA39-4487-9ABE-3027B1A1714D}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{580AB63B-7A4E-4CD0-8683-AAAF5550D764}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{580AB63B-7A4E-4CD0-8683-AAAF5550D764}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{580AB63B-7A4E-4CD0-8683-AAAF5550D764}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{580AB63B-7A4E-4CD0-8683-AAAF5550D764}.Release|Any CPU.Build.0 = Release|Any CPU
		{DAF2BB02-74F0-4C4D-9248-43EC03778F20}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DAF2BB02-74F0-4C4D-9248-43EC03778F20}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DAF2BB02-74F0-4C4D-9248-43EC03778F20}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DAF2BB02-74F0-4C4D-9248-43EC03778F20}.Release|Any CPU.Build.0 = Release|Any CPU
		{39D73BB2-14B4-4014-960D-2CCE6AB47E02}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{39D73BB2-14B4-4014-960D-2CCE6AB47E02}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{39D73BB2-14B4-4014-960D-2CCE6AB47E02}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{39D73BB2-14B4-4014-960D-2CCE6AB47E02}.Release|Any CPU.Build.0 = Release|Any CPU
		{12526E4F-44CA-41DF-8DAE-D8835C9CD516}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{12526E4F-44CA-41DF-8DAE-D8835C9CD516}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{12526E4F-44CA-41DF-8DAE-D8835C9CD516}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{12526E4F-44CA-41DF-8DAE-D8835C9CD516}.Release|Any CPU.Build.0 = Release|Any CPU
		{E42439C9-7026-49CE-888C-DD6B94EF1F5C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E42439C9-7026-49CE-888C-DD6B94EF1F5C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E42439C9-7026-49CE-888C-DD6B94EF1F5C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E42439C9-7026-49CE-888C-DD6B94EF1F5C}.Release|Any CPU.Build.0 = Release|Any CPU
		{A9014AAE-B3CA-4130-896B-9EDA134FD8C6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A9014AAE-B3CA-4130-896B-9EDA134FD8C6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A9014AAE-B3CA-4130-896B-9EDA134FD8C6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A9014AAE-B3CA-4130-896B-9EDA134FD8C6}.Release|Any CPU.Build.0 = Release|Any CPU
		{38FC4C91-0C91-47EE-81A5-5F891E839114}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{38FC4C91-0C91-47EE-81A5-5F891E839114}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{38FC4C91-0C91-47EE-81A5-5F891E839114}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{38FC4C91-0C91-47EE-81A5-5F891E839114}.Release|Any CPU.Build.0 = Release|Any CPU
		{6A87EE70-70BA-4240-A29D-AF33E65DCA05}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6A87EE70-70BA-4240-A29D-AF33E65DCA05}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6A87EE70-70BA-4240-A29D-AF33E65DCA05}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6A87EE70-70BA-4240-A29D-AF33E65DCA05}.Release|Any CPU.Build.0 = Release|Any CPU
		{63B76F77-C165-423A-B8A9-11BC9864C35C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{63B76F77-C165-423A-B8A9-11BC9864C35C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{63B76F77-C165-423A-B8A9-11BC9864C35C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{63B76F77-C165-423A-B8A9-11BC9864C35C}.Release|Any CPU.Build.0 = Release|Any CPU
		{8B76B50E-7A3A-4191-B39A-6686045FAF20}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8B76B50E-7A3A-4191-B39A-6686045FAF20}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8B76B50E-7A3A-4191-B39A-6686045FAF20}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8B76B50E-7A3A-4191-B39A-6686045FAF20}.Release|Any CPU.Build.0 = Release|Any CPU
		{EA8E4102-CA39-4487-9ABE-3027B1A1714D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EA8E4102-CA39-4487-9ABE-3027B1A1714D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EA8E4102-CA39-4487-9ABE-3027B1A1714D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EA8E4102-CA39-4487-9ABE-3027B1A1714D}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {E8FA8B1C-695E-46E4-BCE4-53BEB051E55F}
	EndGlobalSection
EndGlobal
