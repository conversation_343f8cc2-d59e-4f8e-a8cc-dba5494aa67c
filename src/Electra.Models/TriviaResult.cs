namespace Electra.Models;

public class TriviaResult
{
    [Json<PERSON>ropertyName("category")]
    public string Category { get; set; }

    [JsonPropertyName("type")]
    public string Type { get; set; }

    [JsonPropertyName("difficulty")]
    public string Difficulty { get; set; }

    [Json<PERSON>ropertyName("question")]
    public string Question { get; set; }

    [Json<PERSON>ropertyName("correct_answer")]
    public string CorrectAnswer { get; set; }

    [JsonPropertyName("incorrect_answers")]
    public string[] IncorrectAnswers { get; set; }
}