using Microsoft.AspNetCore.Identity;

namespace Electra.Models.ViewModels;

public record RegistrationRequestModel 
{
    [<PERSON>son<PERSON>ropertyName("username")]
    public string Username { get; set; }
        
    [JsonPropertyName("email")]
    public string Email { get; set; }
        
    [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("firstname")]
    public string Firstname { get; set; }
        
    [Json<PERSON>ropertyName("lastname")]
    public string Lastname { get; set; }
        
    [Json<PERSON>ropertyName("password")]
    public string Password { get; set; }
        
    [JsonPropertyName("confirmed_password")]
    public string ConfirmedPassword { get; set; }
        
    [PersonalData]
    [JsonPropertyName("birthday")]
    public DateTime? Birthday { get; set; }
        
    [PersonalData]
    [Json<PERSON>ropertyName("mobile_number")]
    public string MobileNumber { get; set; }
        
    [JsonPropertyName("postal_code")]
    public string PostalCode { get; set; }
        
    [JsonPropertyName("country")]
    public string Country { get; set; }
        
    [Json<PERSON>ropertyName("agreed_tos")]
    public bool AgreedToTos { get; set; }

    [JsonPropertyName("address")]
    public string Address { get; set; }
}