<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <Title>Common models for the Electra Web Platform</Title>
    <Description>Core components for the Electra web application platform</Description>
    <IsPackable>false</IsPackable>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
  </PropertyGroup>
  <ItemGroup>
    <Content Include=".gitignore" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Serilog" Version="4.2.0" />
    <PackageReference Include="ThrowGuard" Version="1.0.7" />
    <PackageReference Include="Z.ExtensionMethods" Version="2.1.1" />
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.Identity.Stores" Version="9.0.4" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Electra.Core\Electra.Core.csproj" />
  </ItemGroup>
</Project>