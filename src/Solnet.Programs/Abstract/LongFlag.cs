namespace Solnet.Programs.Abstract;

/// <summary>
/// Represents a flag using a long for masking.
/// </summary>
public class LongFlag : Flag<ulong>
{

    /// <summary>
    /// Check if the 1st bit is set.
    /// </summary>
    public bool Bit0 => IsKthBitSet(Value, 1);

    /// <summary>
    /// Check if the 2nd bit is set.
    /// </summary>
    public bool Bit1 => IsKthBitSet(Value, 2);

    /// <summary>
    /// Check if the 3rd bit is set.
    /// </summary>
    public bool Bit2 => IsKthBitSet(Value, 3);

    /// <summary>
    /// Check if the 4th bit is set.
    /// </summary>
    public bool Bit3 => IsKthBitSet(Value, 4);

    /// <summary>
    /// Check if the 5th bit is set.
    /// </summary>
    public bool Bit4 => IsKthBitSet(Value, 5);

    /// <summary>
    /// Check if the 6th bit is set.
    /// </summary>
    public bool Bit5 => IsKthBitSet(Value, 6);

    /// <summary>
    /// Check if the 7th bit is set.
    /// </summary>
    public bool Bit6 => IsKthBitSet(Value, 7);

    /// <summary>
    /// Check if the 8th bit is set.
    /// </summary>
    public bool Bit7 => IsKthBitSet(Value, 8);

    /// <summary>
    /// Check if the 9th bit is set.
    /// </summary>
    public bool Bit8 => IsKthBitSet(Value, 9);

    /// <summary>
    /// Check if the 10th bit is set.
    /// </summary>
    public bool Bit9 => IsKthBitSet(Value, 10);

    /// <summary>
    /// Check if the 11th bit is set.
    /// </summary>
    public bool Bit10 => IsKthBitSet(Value, 11);

    /// <summary>
    /// Check if the 12th bit is set.
    /// </summary>
    public bool Bit11 => IsKthBitSet(Value, 12);

    /// <summary>
    /// Check if the 13th bit is set.
    /// </summary>
    public bool Bit12 => IsKthBitSet(Value, 13);

    /// <summary>
    /// Check if the 14th bit is set.
    /// </summary>
    public bool Bit13 => IsKthBitSet(Value, 14);

    /// <summary>
    /// Check if the 15th bit is set.
    /// </summary>
    public bool Bit14 => IsKthBitSet(Value, 15);

    /// <summary>
    /// Check if the 16th bit is set.
    /// </summary>
    public bool Bit15 => IsKthBitSet(Value, 16);

    /// <summary>
    /// Check if the 17th bit is set.
    /// </summary>
    public bool Bit16 => IsKthBitSet(Value, 17);

    /// <summary>
    /// Check if the 18th bit is set.
    /// </summary>
    public bool Bit17 => IsKthBitSet(Value, 18);

    /// <summary>
    /// Check if the 19th bit is set.
    /// </summary>
    public bool Bit18 => IsKthBitSet(Value, 19);

    /// <summary>
    /// Check if the 20th bit is set.
    /// </summary>
    public bool Bit19 => IsKthBitSet(Value, 20);

    /// <summary>
    /// Check if the 21st bit is set.
    /// </summary>
    public bool Bit20 => IsKthBitSet(Value, 21);

    /// <summary>
    /// Check if the 22nd bit is set.
    /// </summary>
    public bool Bit21 => IsKthBitSet(Value, 22);

    /// <summary>
    /// Check if the 23rd bit is set.
    /// </summary>
    public bool Bit22 => IsKthBitSet(Value, 23);

    /// <summary>
    /// Check if the 24th bit is set.
    /// </summary>
    public bool Bit23 => IsKthBitSet(Value, 24);

    /// <summary>
    /// Check if the 25th bit is set.
    /// </summary>
    public bool Bit24 => IsKthBitSet(Value, 25);

    /// <summary>
    /// Check if the 26th bit is set.
    /// </summary>
    public bool Bit25 => IsKthBitSet(Value, 26);

    /// <summary>
    /// Check if the 27th bit is set.
    /// </summary>
    public bool Bit26 => IsKthBitSet(Value, 27);

    /// <summary>
    /// Check if the 28th bit is set.
    /// </summary>
    public bool Bit27 => IsKthBitSet(Value, 28);

    /// <summary>
    /// Check if the 29th bit is set.
    /// </summary>
    public bool Bit28 => IsKthBitSet(Value, 29);

    /// <summary>
    /// Check if the 30th bit is set.
    /// </summary>
    public bool Bit29 => IsKthBitSet(Value, 30);

    /// <summary>
    /// Check if the 31st bit is set.
    /// </summary>
    public bool Bit30 => IsKthBitSet(Value, 31);

    /// <summary>
    /// Check if the 32nd bit is set.
    /// </summary>
    public bool Bit31 => IsKthBitSet(Value, 32);

    /// <summary>
    /// Check if the 33rd bit is set.
    /// </summary>
    public bool Bit32 => IsKthBitSet(Value, 33);

    /// <summary>
    /// Check if the 34th bit is set.
    /// </summary>
    public bool Bit33 => IsKthBitSet(Value, 34);

    /// <summary>
    /// Check if the 35th bit is set.
    /// </summary>
    public bool Bit34 => IsKthBitSet(Value, 35);

    /// <summary>
    /// Check if the 36th bit is set.
    /// </summary>
    public bool Bit35 => IsKthBitSet(Value, 36);

    /// <summary>
    /// Check if the 37th bit is set.
    /// </summary>
    public bool Bit36 => IsKthBitSet(Value, 37);

    /// <summary>
    /// Check if the 38th bit is set.
    /// </summary>
    public bool Bit37 => IsKthBitSet(Value, 38);

    /// <summary>
    /// Check if the 39th bit is set.
    /// </summary>
    public bool Bit38 => IsKthBitSet(Value, 39);

    /// <summary>
    /// Check if the 40th bit is set.
    /// </summary>
    public bool Bit39 => IsKthBitSet(Value, 40);

    /// <summary>
    /// Check if the 41st bit is set.
    /// </summary>
    public bool Bit40 => IsKthBitSet(Value, 41);

    /// <summary>
    /// Check if the 42nd bit is set.
    /// </summary>
    public bool Bit41 => IsKthBitSet(Value, 42);

    /// <summary>
    /// Check if the 43rd bit is set.
    /// </summary>
    public bool Bit42 => IsKthBitSet(Value, 43);

    /// <summary>
    /// Check if the 44th bit is set.
    /// </summary>
    public bool Bit43 => IsKthBitSet(Value, 44);

    /// <summary>
    /// Check if the 45th bit is set.
    /// </summary>
    public bool Bit44 => IsKthBitSet(Value, 45);

    /// <summary>
    /// Check if the 46th bit is set.
    /// </summary>
    public bool Bit45 => IsKthBitSet(Value, 46);

    /// <summary>
    /// Check if the 47th bit is set.
    /// </summary>
    public bool Bit46 => IsKthBitSet(Value, 47);

    /// <summary>
    /// Check if the 48th bit is set.
    /// </summary>
    public bool Bit47 => IsKthBitSet(Value, 48);

    /// <summary>
    /// Check if the 49th bit is set.
    /// </summary>
    public bool Bit48 => IsKthBitSet(Value, 49);

    /// <summary>
    /// Check if the 50th bit is set.
    /// </summary>
    public bool Bit49 => IsKthBitSet(Value, 50);

    /// <summary>
    /// Check if the 51st bit is set.
    /// </summary>
    public bool Bit50 => IsKthBitSet(Value, 51);

    /// <summary>
    /// Check if the 52nd bit is set.
    /// </summary>
    public bool Bit51 => IsKthBitSet(Value, 52);

    /// <summary>
    /// Check if the 53rd bit is set.
    /// </summary>
    public bool Bit52 => IsKthBitSet(Value, 53);

    /// <summary>
    /// Check if the 54th bit is set.
    /// </summary>
    public bool Bit53 => IsKthBitSet(Value, 54);

    /// <summary>
    /// Check if the 55th bit is set.
    /// </summary>
    public bool Bit54 => IsKthBitSet(Value, 55);

    /// <summary>
    /// Check if the 56th bit is set.
    /// </summary>
    public bool Bit55 => IsKthBitSet(Value, 56);

    /// <summary>
    /// Check if the57th bit is set.
    /// </summary>
    public bool Bit56 => IsKthBitSet(Value, 57);

    /// <summary>
    /// Check if the 58th bit is set.
    /// </summary>
    public bool Bit57 => IsKthBitSet(Value, 58);

    /// <summary>
    /// Check if the 59th bit is set.
    /// </summary>
    public bool Bit58 => IsKthBitSet(Value, 59);

    /// <summary>
    /// Check if the 60th bit is set.
    /// </summary>
    public bool Bit59 => IsKthBitSet(Value, 60);

    /// <summary>
    /// Check if the 61st bit is set.
    /// </summary>
    public bool Bit60 => IsKthBitSet(Value, 61);

    /// <summary>
    /// Check if the 62nd bit is set.
    /// </summary>
    public bool Bit61 => IsKthBitSet(Value, 62);

    /// <summary>
    /// Check if the 63rd bit is set.
    /// </summary>
    public bool Bit62 => IsKthBitSet(Value, 63);

    /// <summary>
    /// Check if the 64th bit is set.
    /// </summary>
    public bool Bit63 => IsKthBitSet(Value, 64);

    /// <summary>
    /// Initialize the flags with the given ulong.
    /// </summary>
    /// <param name="mask">The ulong to use.</param>
    public LongFlag(ulong mask) : base(mask) { }
}