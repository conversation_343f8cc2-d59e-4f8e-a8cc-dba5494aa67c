server {
    charset UTF-8;
    server_name xamarui.com www.xamarui.com xamweb;
    # in the 'http', 'server', or 'location' context
    aio threads=default;
    # to boost I/O on HDD we can disable access logs
    access_log off;
    
    # for more verbose logging in nginx - remove above 
    #access_log /var/log/nginx/access.log;
    #error_log /var/log/nginx/error.log;
    
    #set up logging - investigate more on location & what is pertinent to log (var/log/nginx/....)
    #    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
    #    '$status $body_bytes_sent "$http_referer" '
    #    '"$http_user_agent" "$http_x_forwarded_for"';
    #    access_log  /Users/<USER>/Projects/nginxdemo/logs/access.log  main;

    limit_conn_zone $binary_remote_addr zone=addr:10m;
    limit_conn addr 100;

    # image caching breaking in production - investigate (actually cdn should cache this)
    #If you are fingerprinting your static resources (you should be) then you can get very significant 
    #performance boost from letting clients cache these resources for a long time:
#    location ~* \.(ttf|mp3|mp4|webm|ogg|jpg|jpeg|png|gif|ico|css|js)$ {
#        expires 365m;
#        tcp_nodelay off;
#        add_header Vary Accept-Endcoding;
#        access_log off;
#    }
    
    location / {
        # Reject requests with unsupported HTTP method
        if ($request_method !~ ^(GET|POST|HEAD|OPTIONS|PUT|DELETE)$) {
            return 405;
        }
        proxy_redirect          off;
        proxy_pass http://localhost:5000;  # can't proxy via ssl to https://  ?? research
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection $http_connection;
            proxy_set_header Host $host;
            proxy_cache_bypass $http_upgrade;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_ssl_session_reuse off;
            # try_files $uri $uri/ =404;
            # limit_req  zone=one burst=10 nodelay; # research this line & what it does
            # from proxy.conf
            client_max_body_size    10m;
            client_body_buffer_size 128k;
            proxy_connect_timeout   90;
            proxy_send_timeout      90;
            proxy_read_timeout      90;
            proxy_buffers           32 4k;
            #set up a proxy cache location
            proxy_temp_path /tmp/cache/temp;
    }

    #just cache image files, if not in cache ask Kestrel
    # how can I include multiple locations??
    #location /images/ {
#    location ~* \.(ttf|mp3|mp4|webm|ogg|jpg|jpeg|png|gif|ico|css|js)$ {
#        #use the proxy to save files
#        proxy_cache aspnetcache;
#        proxy_cache_valid  200 302 60m;
#    }

    listen 443 http2 ssl; # managed by Certbot
    ssl_certificate /etc/letsencrypt/live/xamarui.com/fullchain.pem; # managed by Certbot
    ssl_certificate_key /etc/letsencrypt/live/xamarui.com/privkey.pem; # managed by Certbot
    include /etc/letsencrypt/options-ssl-nginx.conf; # managed by Certbot
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem; # managed by Certbot
}

    server {
    if ($host = www.xamarui.com) {
        return 301 https://$host$request_uri;
    } # managed by Certbot


    if ($host = xamarui.com) {
        return 301 https://$host$request_uri;
    } # managed by Certbot


    listen 80;
    server_name xamarui.com www.xamarui.com xamweb;
    return 404; # managed by Certbot
}