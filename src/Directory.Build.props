<Project>
    <PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
        <LangVersion>default</LangVersion>
        <Nullable>enable</Nullable>
        <ImplicitUsings>false</ImplicitUsings>
        <LangVersion>default</LangVersion>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
        <NoWarn>1701;1702;1591;</NoWarn>
    </PropertyGroup>
    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
        <NoWarn>1701;1702;1591;</NoWarn>
    </PropertyGroup>

    <PropertyGroup Label="Package information">
        <Version>1.0.0-alpha</Version>
        <Authors>Electra, Microbians, bbqchickenrobot</Authors>
        <Company>Microbians</Company>
        <Copyright>Copyright (c) 2023 Microbians</Copyright>
        <PackageTags>asp.net efcore Electra</PackageTags>
<!--        <PackageIcon>../../assets/electra2.png</PackageIcon>-->
        <PackageProjectUrl>https://github.com/microbian-systems/Electra</PackageProjectUrl>
        <RepositoryUrl>https://github.com/microbian-systems/Electra</RepositoryUrl>
        <RepositoryType>git</RepositoryType>
        <PublishRepositoryUrl>true</PublishRepositoryUrl>
        <IncludeSymbols>true</IncludeSymbols>
        <SymbolPackageFormat>snupkg</SymbolPackageFormat>
        <EmbedUntrackedSources>true</EmbedUntrackedSources>
        <GenerateDocumentationFile>true</GenerateDocumentationFile>
    </PropertyGroup>
</Project>