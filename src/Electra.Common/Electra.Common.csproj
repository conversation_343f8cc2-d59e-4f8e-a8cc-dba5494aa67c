<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <Title>Electra Common Utilities</Title>
    <IsPackable>false</IsPackable>
    <RootNamespace>Electra.Common</RootNamespace>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.CodeAnalysis.CSharp.Workspaces" Version="4.13.0" />
    <PackageReference Include="Microsoft.CodeAnalysis.Workspaces.Common" Version="4.13.0" />
    <PackageReference Include="Polly" Version="8.5.2" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.4" />
    <PackageReference Include="System.Text.Json" Version="9.0.4" />
    <PackageReference Include="ThrowGuard" Version="1.0.7" />
  </ItemGroup>
</Project>