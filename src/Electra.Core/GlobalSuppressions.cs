
// This file is used by Code Analysis to maintain SuppressMessage 
// attributes that are applied to this project.
// Project-level suppressions either have no target or are given 
// a specific target and scoped to a namespace, type, member, etc.

[assembly: System.Diagnostics.CodeAnalysis.SuppressMessage("CodeQuality", "Serilog004:Constant MessageTemplate verifier", Justification = "<Pending>", Scope = "member", Target = "~M:Electra.Core.AzureBlobStorageClient.PostAsync(System.IO.MemoryStream,System.String,System.String,System.String,System.Boolean,System.String,System.Boolean)~System.Threading.Tasks.Task")]
[assembly: System.Diagnostics.CodeAnalysis.SuppressMessage("CodeQuality", "Serilog004:Constant MessageTemplate verifier", Justification = "<Pending>", Scope = "member", Target = "~M:Electra.Core.Helpers.Config.GetJobCommand``1(Serilog.ILogger)~``0")]
[assembly: System.Diagnostics.CodeAnalysis.SuppressMessage("Security", "SEC0116:Path Tampering Unvalidated File Path", Justification = "<Pending>", Scope = "member", Target = "~M:Electra.Core.XsdSchemaValidator.Validate(System.String)~System.Boolean")]
[assembly: System.Diagnostics.CodeAnalysis.SuppressMessage("Security", "SEC0116:Path Tampering Unvalidated File Path", Justification = "<Pending>", Scope = "member", Target = "~M:Electra.Core.XsdSchemaValidator.AddSchema(System.String)~System.Boolean")]