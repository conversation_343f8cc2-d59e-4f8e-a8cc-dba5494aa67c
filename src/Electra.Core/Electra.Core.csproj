<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <Title>Core Package for the Electra Web Platform</Title>
    <Description>Core components for the Electra web application platform</Description>
    <IsPackable>false</IsPackable>
    <RootNamespace>Electra.Core</RootNamespace>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="9.0.4" />
    <PackageReference Include="SecretSharingDotNet" Version="0.12.0" />
    <PackageReference Include="Serilog" Version="4.2.0" />
    <PackageReference Include="System.Text.Json" Version="9.0.4" />
    <PackageReference Include="ThrowGuard" Version="1.0.7" />
    <PackageReference Include="WindowsAzure.Storage" Version="9.3.3" />
    <PackageReference Include="Z.ExtensionMethods" Version="2.1.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.Configuration.UserSecrets" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.Identity.Stores" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="9.0.4" />
    <PackageReference Include="System.Configuration.ConfigurationManager" Version="9.0.4" />
  </ItemGroup>
  <ItemGroup>
    <Content Include=".gitignore" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Electra.Common\Electra.Common.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="DataStructures\Trees\" />
  </ItemGroup>
</Project>