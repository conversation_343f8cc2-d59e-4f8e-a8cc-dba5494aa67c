namespace Electra.Core;

public enum ErrorCodes
{
    InputNull = 1000,
    InvalidEmail = 1001,
    InvalidPassword = 1002,
    InvalidToken = 1003,
    InvalidCredentials = 1004,
    UserNotFound = 1005,
    UserAlreadyExists = 1006,
    InvalidRole = 1007,
    InvalidClaim = 1008,
    InvalidIssuer = 1009,
    InvalidAudience = 1010,
    InvalidSecret = 1011,
    InvalidExpiration = 1012,
    InvalidAlgorithm = 1013,
    InvalidTokenFormat = 1014,
    InvalidTokenSignature = 1015,
    InvalidTokenClaims = 1016,
    InvalidTokenAudience = 1017,
    InvalidTokenIssuer = 1018,
    InvalidTokenExpiration = 1019,
    InvalidTokenNotBefore = 1020,
    InvalidTokenIssuedAt = 1021,
    InvalidTokenSubject = 1022,
    InvalidTokenId = 1023,
    InvalidTokenType = 1024,
    InvalidTokenAlgorithm = 1025,
    InvalidTokenKey = 1026,
    InvalidTokenLifetime = 1027,
    InvalidTokenHeader = 1028,
    InvalidTokenPayload = 1029,
    InvalidTokenSignatureAlgorithm = 1030,
    InvalidUsernameOrPassword = 1031,

}