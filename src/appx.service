[Unit]
Description=AppX Web Service on Ubuntu (systemctl)

[Service]
WorkingDirectory=/var/www/xamarui.com
ExecStart=/snap/bin/dotnet /var/www/xamarui.com/AppX.Web.dll
Restart=always
# Restart service after 10 seconds if the dotnet service crashes:
RestartSec=10
KillSignal=SIGINT
SyslogIdentifier=appx-web
User=www-data
Environment=ASPNETCORE_ENVIRONMENT=Production
Environment=DOTNET_PRINT_TELEMETRY_MESSAGE=false

[Install]
WantedBy=multi-user.target