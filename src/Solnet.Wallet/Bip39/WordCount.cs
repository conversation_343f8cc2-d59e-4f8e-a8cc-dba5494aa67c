namespace Solnet.Wallet.Bip39
{
    /// <summary>
    /// Specifies the available lengths for the mnemonic.
    /// </summary>
    public enum WordCount
    {
        /// <summary>
        /// Twelve words.
        /// </summary>
        Twelve = 12,

        /// <summary>
        /// Fifteen words.
        /// </summary>
        Fifteen = 15,

        /// <summary>
        /// Eighteen words.
        /// </summary>
        Eighteen = 18,

        /// <summary>
        /// Twenty one words.
        /// </summary>
        TwentyOne = 21,

        /// <summary>
        /// Twenty four words.
        /// </summary>
        TwentyFour = 24
    }
}