// unset

namespace Solnet.Wallet.Bip39;

/// <summary>
/// Specifies the available languages for mnemonic generation.
/// </summary>
public enum Language
{
    /// <summary>
    /// English.
    /// </summary>
    English,
    /// <summary>
    /// Japanese.
    /// </summary>
    Japanese,
    /// <summary>
    /// Spanish.
    /// </summary>
    Spanish,
    /// <summary>
    /// Simplified Chinese.
    /// </summary>
    ChineseSimplified,
    /// <summary>
    /// Traditional Chinese.
    /// </summary>
    ChineseTraditional,
    /// <summary>
    /// French.
    /// </summary>
    French,
    /// <summary>
    /// Brazilian portuguese.
    /// </summary>
    PortugueseBrazil,
    /// <summary>
    /// Czech.
    /// </summary>
    Czech,
    /// <summary>
    /// Unknown.
    /// </summary>
    Unknown
};