param(
    [Parameter(Mandatory = $true)]
    [string]$CommitMessage
)

# Define the project directories
$projects = @(
    "Solnet",
    "Solnet.Anchor",
    "Solnet.Metaplex",
    "Solnet.Moonshot",
    "Solnet.Pyth",
    "Solnet.Raydium",
    "Solnet.Zeta",
    "Electra"
)

# Store the current directory
$originalLocation = Get-Location

Write-Host "Starting git operations for all projects..." -ForegroundColor Green
Write-Host "Commit message: '$CommitMessage'" -ForegroundColor Yellow
Write-Host ""

foreach ($project in $projects)
{
    if (Test-Path $project)
    {
        Write-Host "Processing: $project" -ForegroundColor Cyan

        try
        {
            # Change to project directory
            Set-Location $project

            # Check if it's a git repository
            if (Test-Path ".git")
            {
                # Add all changes
                Write-Host "  Running: git add ." -ForegroundColor Gray
                git add .

                # Check if there are changes to commit
                $status = git status --porcelain
                if ($status)
                {
                    # Commit with the provided message
                    Write-Host "  Running: git commit -am `"$CommitMessage`"" -ForegroundColor Gray
                    git commit -am "$CommitMessage"
                    Write-Host "   Committed successfully" -ForegroundColor Green
                }
                else
                {
                    Write-Host "   No changes to commit" -ForegroundColor Yellow
                }
            }
            else
            {
                Write-Host "   Not a git repository, skipping" -ForegroundColor Yellow
            }
        }
        catch
        {
            Write-Host "   Error processing $project`: $_" -ForegroundColor Red
        }
        finally
        {
            # Return to original directory
            Set-Location $originalLocation
        }

        Write-Host ""
    }
    else
    {
        Write-Host " Directory '$project' not found, skipping" -ForegroundColor Yellow
        Write-Host ""
    }
}

Write-Host "Git operations completed!" -ForegroundColor Green
