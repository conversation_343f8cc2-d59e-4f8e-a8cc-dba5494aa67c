<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <IsPackable>false</IsPackable>
    <OutputType>Library</OutputType>
    <LangVersion>default</LangVersion>
    <RootNamespace>Electra.Core.Tests</RootNamespace>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="MediatR" Version="12.5.0" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.13.0" />
    <PackageReference Include="xunit" Version="2.9.3" />
    <PackageReference Include="Z.ExtensionMethods" Version="2.1.1" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Electra.Core\Electra.Core.csproj" />
  </ItemGroup>
</Project>