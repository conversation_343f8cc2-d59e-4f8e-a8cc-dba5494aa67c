// Global using directives

global using System;
global using System.Collections.Generic;
global using System.IdentityModel.Tokens.Jwt;
global using System.Linq;
global using System.Net;
global using System.Security.Claims;
global using System.Security.Cryptography;
global using System.Text;
global using System.Text.Json.Serialization;
global using System.Threading.Tasks;
global using System.Web;
global using FluentEmail.Core;
global using JasperFx.Core;
global using Electra.Common;
global using Electra.Common.Extensions;
global using Electra.Core.Identity;
global using Electra.Models;
global using Electra.Models.ViewModels;
global using Electra.Persistence;
global using Electra.Persistence.Entities;
global using Electra.Services.Features;
global using Electra.Services.Geo;
global using Electra.Services.Models;
global using Microsoft.AspNetCore.Http;
global using Microsoft.AspNetCore.Identity;
global using Microsoft.Extensions.Logging;
global using Microsoft.Extensions.Options;
global using Microsoft.IdentityModel.Tokens;