<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
      <RootNamespace>Electra.Piranha</RootNamespace>
        <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="Microsoft.AspNetCore.Authorization" Version="9.0.4" />
      <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.4" />
      <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.4" />
      <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.4" />
      <PackageReference Include="Microsoft.Extensions.Options" Version="9.0.4" />
      <PackageReference Include="Piranha" Version="12.0.0" />
      <PackageReference Include="Piranha.AspNetCore" Version="12.0.0" />
      <PackageReference Include="Piranha.AspNetCore.Hosting" Version="12.0.0" />
      <PackageReference Include="Piranha.AspNetCore.Identity" Version="12.0.0" />
      <PackageReference Include="Piranha.AspNetCore.SimpleSecurity" Version="10.4.0" />
      <PackageReference Include="Piranha.Data.EF" Version="12.0.0" />
      <PackageReference Include="Piranha.Manager.LocalAuth" Version="12.0.0" />
      <PackageReference Include="Piranha.WebApi" Version="12.0.0" />
      <PackageReference Include="Scrutor" Version="6.0.1" />
      <PackageReference Include="ThrowGuard" Version="1.0.7" />
    </ItemGroup>

</Project>