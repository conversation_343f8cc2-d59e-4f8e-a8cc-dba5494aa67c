<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <Title>SignalR hubs for the Electra Web Platform</Title>
    <Description>Core components for the Electra web application platform</Description>
    <IsPackable>false</IsPackable>
    <RootNamespace>Electra.SignalR</RootNamespace>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client.Core" Version="9.0.4" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Common" Version="9.0.4" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.StackExchangeRedis" Version="9.0.4" />
    <PackageReference Include="Serilog" Version="4.2.0" />
    <PackageReference Include="ThrowGuard" Version="1.0.7" />
    <PackageReference Include="Z.ExtensionMethods" Version="2.1.1" />
  </ItemGroup>
</Project>