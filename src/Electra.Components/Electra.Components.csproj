<Project Sdk="Microsoft.NET.Sdk.Razor">

  <PropertyGroup>
    <RootNamespace>Electra.Components</RootNamespace>
  </PropertyGroup>


  <ItemGroup>
    <SupportedPlatform Include="browser" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Blazor-ApexCharts" Version="6.0.0" />
    <PackageReference Include="Blazored.FluentValidation" Version="2.2.0" />
    <PackageReference Include="Blazored.LocalStorage" Version="4.5.0" />
    <PackageReference Include="Blazored.Menu" Version="2.2.0" />
    <PackageReference Include="Blazored.Modal" Version="7.3.1" />
    <PackageReference Include="Blazored.SessionStorage" Version="2.4.0" />
    <PackageReference Include="Blazored.TextEditor" Version="1.1.3" />
    <PackageReference Include="Blazored.Toast" Version="4.2.1" />
    <PackageReference Include="Blazored.Video" Version="1.0.1" />
    <PackageReference Include="Blazorise" Version="1.7.6" />
    <PackageReference Include="Blazorise.Animate" Version="1.7.6" />
    <PackageReference Include="Blazorise.AntDesign" Version="1.7.6" />
    <PackageReference Include="Blazorise.Bootstrap" Version="1.7.6" />
    <PackageReference Include="Blazorise.Bootstrap5" Version="1.7.6" />
    <PackageReference Include="Blazorise.Bulma" Version="1.7.6" />
    <PackageReference Include="Blazorise.Captcha" Version="1.7.6" />
    <PackageReference Include="Blazorise.Captcha.ReCaptcha" Version="1.7.6" />
    <PackageReference Include="Blazorise.Charts" Version="1.7.6" />
    <PackageReference Include="Blazorise.Charts.Annotation" Version="1.7.6" />
    <PackageReference Include="Blazorise.Charts.DataLabels" Version="1.7.6" />
    <PackageReference Include="Blazorise.Charts.Streaming" Version="1.7.6" />
    <PackageReference Include="Blazorise.Charts.Trendline" Version="1.7.6" />
    <PackageReference Include="Blazorise.Components" Version="1.7.6" />
    <PackageReference Include="Blazorise.Cropper" Version="1.7.6" />
    <PackageReference Include="Blazorise.DataGrid" Version="1.7.6" />
    <PackageReference Include="Blazorise.FluentValidation" Version="1.7.6" />
    <PackageReference Include="Blazorise.Generator.Features" Version="1.7.6" />
    <PackageReference Include="Blazorise.Icons.Bootstrap" Version="1.7.6" />
    <PackageReference Include="Blazorise.Icons.FluentUI" Version="1.7.6" />
    <PackageReference Include="Blazorise.Icons.FontAwesome" Version="1.7.6" />
    <PackageReference Include="Blazorise.Icons.Material" Version="1.7.6" />
    <PackageReference Include="Blazorise.LoadingIndicator" Version="1.7.6" />
    <PackageReference Include="Blazorise.LottieAnimation" Version="1.7.6" />
    <PackageReference Include="Blazorise.Markdown" Version="1.7.6" />
    <PackageReference Include="Blazorise.Material" Version="1.7.6" />
    <PackageReference Include="Blazorise.QRCode" Version="1.7.6" />
    <PackageReference Include="Blazorise.RichTextEdit" Version="1.7.6" />
    <PackageReference Include="Blazorise.Sidebar" Version="1.7.6" />
    <PackageReference Include="Blazorise.SignaturePad" Version="1.7.6" />
    <PackageReference Include="Blazorise.Snackbar" Version="1.7.6" />
    <PackageReference Include="Blazorise.SpinKit" Version="1.7.6" />
    <PackageReference Include="Blazorise.Splitter" Version="1.7.6" />
    <PackageReference Include="Blazorise.Tailwind" Version="1.7.6" />
    <PackageReference Include="Blazorise.TreeView" Version="1.7.6" />
    <PackageReference Include="Blazorise.Video" Version="1.7.6" />
    <PackageReference Include="LightweightCharts.Blazor" Version="5.0.3.1" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="9.0.4" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Authentication" Version="9.0.4" />
    <PackageReference Include="Syncfusion.Blazor" Version="29.1.41" />
    <PackageReference Include="Syncfusion.Blazor.BulletChart" Version="29.1.41" />
    <PackageReference Include="Syncfusion.Blazor.Buttons" Version="29.1.41" />
    <PackageReference Include="Syncfusion.Blazor.Cards" Version="29.1.41" />
    <PackageReference Include="Syncfusion.Blazor.Charts" Version="29.1.41" />
    <PackageReference Include="Syncfusion.Blazor.Data" Version="29.1.41" />
    <PackageReference Include="Syncfusion.Blazor.Grid" Version="29.1.41" />
    <PackageReference Include="Syncfusion.Blazor.PdfViewer" Version="27.1.58" />
    <PackageReference Include="Syncfusion.Blazor.Popups" Version="29.1.41" />
    <PackageReference Include="Syncfusion.Blazor.ProgressBar" Version="29.1.41" />
    <PackageReference Include="Syncfusion.Blazor.Spinner" Version="29.1.41" />
    <PackageReference Include="Syncfusion.Blazor.SplitButtons" Version="29.1.41" />
    <PackageReference Include="Syncfusion.Blazor.StockChart" Version="29.1.41" />
    <PackageReference Include="ThrowGuard" Version="1.0.7" />
  </ItemGroup>

</Project>