<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        
        <Title>Workflows for the Electra Web Platform</Title>
        <PackageId>Electra.Workflows</PackageId>
        <Description>This package contains the core workflows for the Electra web application platform</Description>
        <OutputType>Library</OutputType>
        <IsPackable>true</IsPackable>
        <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
        <RootNamespace>Electra.Workflows</RootNamespace>
        
    </PropertyGroup>
    <ItemGroup>
        <PackageReference Include="Automapper" Version="14.0.0" />
        <PackageReference Include="Elsa" Version="3.3.5" />
        <PackageReference Include="Elsa.Abstractions" Version="2.15.1" />
        <PackageReference Include="Elsa.Activities.Console" Version="2.15.1" />
        <PackageReference Include="Elsa.Activities.ControlFlow" Version="1.5.5.1943" />
        <PackageReference Include="Elsa.Activities.Email" Version="2.15.1" />
        <PackageReference Include="Elsa.Activities.Http" Version="2.15.1" />
        <PackageReference Include="Elsa.Activities.Timers" Version="1.5.5.1943" />
        <PackageReference Include="Elsa.Activities.UserTask" Version="2.15.1" />
        <PackageReference Include="Elsa.Activities.Workflows" Version="1.5.5.1943" />
        <PackageReference Include="Elsa.Core" Version="2.15.1" />
        <PackageReference Include="Elsa.Dashboard" Version="1.5.5.1943" />
        <PackageReference Include="Elsa.Persistence.EntityFrameworkCore" Version="1.5.5.1943" />
        <PackageReference Include="Elsa.Persistence.MongoDb" Version="2.15.1" />
        <PackageReference Include="Elsa.Scripting.JavaScript" Version="2.15.1" />
        <PackageReference Include="Elsa.Scripting.Liquid" Version="2.15.1" />
        <PackageReference Include="Elsa.WorkflowDesigner" Version="1.5.5.1943" />
        <PackageReference Include="MediatR" Version="12.5.0" />
        <PackageReference Include="Stateless" Version="5.17.0" />
    </ItemGroup>
    <ItemGroup>
      <None Update="microbians.png">
        <Pack>True</Pack>
        <PackagePath></PackagePath>
      </None>
    </ItemGroup>
</Project>
