<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="BifrostSecurity" Version="1.0.1" />
    <PackageReference Include="Portable.BouncyCastle" Version="1.9.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Solnet.Wallet\Solnet.Wallet.csproj" />
  </ItemGroup>

  <Import Project="..\..\SharedBuildProperties.props" />
</Project>
